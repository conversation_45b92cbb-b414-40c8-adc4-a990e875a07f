import React, { useState } from 'react'
import { MentionInput } from './mention-input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card'
import { Badge } from './badge'

/**
 * Demo component to showcase the dual-state mention system
 * This demonstrates how the MentionInput component:
 * 1. Accepts email-based mentions from backend (@<EMAIL>)
 * 2. Displays friendly names in the UI (@UserName)
 * 3. Converts back to email format for backend submission
 */
export const MentionInputDemo: React.FC = () => {
  const [emailValue, setEmailValue] = useState('Hello @<EMAIL> and @<EMAIL>! How are you?')
  const [displayValue] = useState('')

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Dual-State Mention System Demo</CardTitle>
          <CardDescription>
            This demo shows how the enhanced MentionInput component handles mentions with a dual-state system:
            email-based backend format and display-friendly frontend format.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Demo Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Enhanced Mention Input</label>
            <div className="border rounded-lg p-3 min-h-[100px]">
              <MentionInput
                hubId={1}
                value={emailValue}
                onChange={setEmailValue}
                placeholder="Type @ to mention someone..."
                className="w-full"
              >
                {({ 
                  ref, 
                  contentEditable, 
                  suppressContentEditableWarning,
                  dangerouslySetInnerHTML,
                  onChange,
                  onKeyDown,
                  onSelect,
                  placeholder,
                  className
                }) => (
                  <div
                    ref={ref}
                    contentEditable={contentEditable}
                    suppressContentEditableWarning={suppressContentEditableWarning}
                    dangerouslySetInnerHTML={dangerouslySetInnerHTML}
                    onInput={onChange}
                    onKeyDown={onKeyDown}
                    onSelect={onSelect}
                    data-placeholder={placeholder}
                    dir="ltr"
                    className={`
                      min-h-[60px] p-2 outline-none resize-none
                      empty:before:content-[attr(data-placeholder)]
                      empty:before:text-muted-foreground
                      empty:before:pointer-events-none
                      ${className}
                    `}
                    style={{
                      direction: 'ltr',
                      textAlign: 'left',
                      unicodeBidi: 'embed',
                      wordBreak: 'break-word',
                      whiteSpace: 'pre-wrap'
                    }}
                  />
                )}
              </MentionInput>
            </div>
          </div>

          {/* State Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Backend Format (Email-based)</label>
                <Badge variant="secondary">Source of Truth</Badge>
              </div>
              <div className="bg-muted p-3 rounded-lg text-sm font-mono break-all">
                {emailValue || 'Empty'}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Display Format (User-friendly)</label>
                <Badge variant="outline">UI Only</Badge>
              </div>
              <div className="bg-muted p-3 rounded-lg text-sm">
                <div 
                  dangerouslySetInnerHTML={{ 
                    __html: displayValue || '<span class="text-muted-foreground">Empty</span>' 
                  }} 
                />
              </div>
            </div>
          </div>

          {/* Feature Highlights */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Key Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 shrink-0" />
                <div>
                  <div className="font-medium text-sm">Dual-State Architecture</div>
                  <div className="text-xs text-muted-foreground">
                    Email format for backend, display names for UI
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 shrink-0" />
                <div>
                  <div className="font-medium text-sm">Styled Mention Tokens</div>
                  <div className="text-xs text-muted-foreground">
                    Blue highlights with hover effects
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 shrink-0" />
                <div>
                  <div className="font-medium text-sm">Atomic Deletion</div>
                  <div className="text-xs text-muted-foreground">
                    Delete entire mention tokens, not partial text
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 shrink-0" />
                <div>
                  <div className="font-medium text-sm">Keyboard Navigation</div>
                  <div className="text-xs text-muted-foreground">
                    Arrow keys, Enter, Tab, Escape support
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Test Cases</label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setEmailValue('Hello @<EMAIL>!')}
                className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
              >
                Single Mention
              </button>
              <button
                onClick={() => setEmailValue('Team meeting with @<EMAIL> and @<EMAIL> at 3pm')}
                className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
              >
                Multiple Mentions
              </button>
              <button
                onClick={() => setEmailValue('Regular text without any mentions')}
                className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              >
                No Mentions
              </button>
              <button
                onClick={() => setEmailValue('')}
                className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                Clear
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
